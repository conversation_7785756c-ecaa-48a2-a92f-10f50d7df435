<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\HaiboService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class HaiboController extends Controller
{
    protected HaiboService $haiboService;

    public function __construct(HaiboService $haiboService)
    {
        $this->haiboService = $haiboService;
    }

    /**
     * 创建/修改配送商门店接口
     *
     * POST /api/haibo/store
     * Content-Type: application/x-www-form-urlencoded
     */
    public function createOrUpdateStore(Request $request)
    {
        try {
            // 记录海博请求信息
            Log::channel('haibo')->info('海博创建/修改配送商门店请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数 - 根据海博接口规范
            $validator = Validator::make($request->all(), [
                'contactPhone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            ], [
                'contactPhone.required' => '门店联系人电话不能为空',
                'contactPhone.regex' => '手机号格式不正确',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博请求参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => 1, // 海博规范：非0表示失败
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理业务逻辑
            $result = $this->haiboService->createOrUpdateStore($request->all());

            Log::channel('haibo')->info('海博门店操作成功', $result);

            // 按照海博接口规范返回响应
            return response()->json([
                'code' => 0, // 海博规范：0表示成功
                'message' => '成功',
                'data' => [
                    'carrierShopId' => $result['data']['carrier_shop_id'],
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博门店操作异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 1, // 海博规范：非0表示失败
                'message' => '操作失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 询价接口 - 获取预估配送费、配送时间等信息
     *
     * POST /api/haibo/valuating
     * Content-Type: application/json
     */
    public function valuating(Request $request)
    {
        try {
            // 记录海博询价请求信息
            Log::channel('haibo')->info('海博询价请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'tradeOrderSource' => 'required|integer|min:1',
                'orderId' => 'required|string|max:45',
                'serviceCode' => 'required|string',
                'recipientName' => 'required|string|max:256',
                'recipientPhone' => 'required|string|max:64',
                'recipientAddress' => 'required|string|max:512',
                'recipientLng' => 'required|integer',
                'recipientLat' => 'required|integer',
                'prebook' => 'required|integer|in:0,1',
                'expectedDeliveryTime' => 'nullable|integer',
                'expectedLeftDeliveryTime' => 'nullable|integer',
                'expectedRightDeliveryTime' => 'nullable|integer',
                'expectedPickupTime' => 'nullable|integer',
                'insuredMark' => 'nullable|integer|in:0,1',
                'totalValue' => 'nullable|numeric|min:0',
                'totalWeight' => 'required|integer|min:1',
                'totalVolume' => 'nullable|integer|min:0',
                'riderPickMethod' => 'nullable|integer|in:0,1',
                'goodsDetails' => 'nullable|string|max:10240',
                'carrierMerchantId' => 'required|string',
                'extInfo' => 'nullable|string',
                'carrierShopId' => 'nullable|string',
                'senderLng' => 'required|integer',
                'senderLat' => 'required|integer',
                'senderName' => 'required|string',
                'senderContract' => 'required|string',
                'senderAddressDetail' => 'required|string',
                'carModelCode' => 'nullable|string',
                'category' => 'nullable|integer',
            ], [
                'tradeOrderSource.required' => '交易订单来源不能为空',
                'orderId.required' => '海博平台订单号不能为空',
                'serviceCode.required' => '配送商配送服务不能为空',
                'recipientName.required' => '收件人姓名不能为空',
                'recipientPhone.required' => '收件人电话不能为空',
                'recipientAddress.required' => '收件人地址不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'prebook.required' => '是否即时单参数不能为空',
                'prebook.in' => '是否即时单参数值错误',
                'totalWeight.required' => '物品重量不能为空',
                'totalWeight.min' => '物品重量必须大于0',
                'carrierMerchantId.required' => '配送商ID不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
                'senderName.required' => '发件人姓名不能为空',
                'senderContract.required' => '发件人电话不能为空',
                'senderAddressDetail.required' => '发件人详细地址不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博询价参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理询价逻辑
            $result = $this->haiboService->valuating($request->all());

            Log::channel('haibo')->info('海博询价处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博询价异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

}
